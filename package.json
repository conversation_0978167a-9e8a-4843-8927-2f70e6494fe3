{"name": "knowledge-base-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^0.0.66", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.4", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.35.0", "ai": "^3.4.32", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.4", "critters": "^0.0.24", "date-fns": "^4.1.0", "embla-carousel-react": "^8.3.0", "input-otp": "^1.4.1", "lucide-react": "^0.460.0", "next": "15.1.0", "next-themes": "^0.4.6", "pdf-parse": "^1.1.1", "react": "^19.0.0", "react-day-picker": "^9.4.2", "react-dom": "^19.0.0", "react-hook-form": "^7.53.2", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.13.3", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "sonner": "^1.7.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.1", "zod": "^3.23.8"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/node": "^22.10.1", "@types/pdf-parse": "^1.1.4", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "autoprefixer": "^10.4.21", "eslint": "^9.17.0", "eslint-config-next": "15.1.0", "postcss": "^8.5.5", "tailwindcss": "^3.4.17", "typescript": "^5.7.2"}}