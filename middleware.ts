import { NextRequest, NextResponse } from 'next/server';
import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';

// Initialize Redis for rate limiting (use Upstash for serverless)
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

// Different rate limits for different endpoints
const rateLimits = {
  // Public chat endpoints - more restrictive
  chat: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(10, '5 m'), // 10 requests per 5 minutes
    analytics: true,
  }),
  
  // Admin operations - very restrictive
  admin: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(5, '10 m'), // 5 requests per 10 minutes
    analytics: true,
  }),
  
  // File uploads - extremely restrictive
  upload: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(2, '1 h'), // 2 uploads per hour
    analytics: true,
  }),
  
  // URL processing - restrictive
  urlProcess: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(3, '30 m'), // 3 URLs per 30 minutes
    analytics: true,
  }),
  
  // General API - moderate
  api: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(30, '1 m'), // 30 requests per minute
    analytics: true,
  }),
};

// Get client IP address
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (cfConnectingIP) return cfConnectingIP;
  if (realIP) return realIP;
  if (forwarded) return forwarded.split(',')[0].trim();
  
  return request.ip || 'unknown';
}

// Determine rate limit type based on path
function getRateLimitType(pathname: string): keyof typeof rateLimits {
  if (pathname.includes('/api/chat')) return 'chat';
  if (pathname.includes('/api/admin')) return 'admin';
  if (pathname.includes('/api/upload-pdf')) return 'upload';
  if (pathname.includes('/api/process-url')) return 'urlProcess';
  if (pathname.startsWith('/api/')) return 'api';
  
  return 'api'; // default
}

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  
  // Skip rate limiting for static files and non-API routes
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/static/') ||
    pathname.includes('.') ||
    !pathname.startsWith('/api/')
  ) {
    return NextResponse.next();
  }

  try {
    const ip = getClientIP(request);
    const rateLimitType = getRateLimitType(pathname);
    const rateLimit = rateLimits[rateLimitType];
    
    // Apply rate limiting
    const { success, limit, reset, remaining } = await rateLimit.limit(ip);
    
    // Add rate limit headers
    const response = success ? NextResponse.next() : new NextResponse('Rate limit exceeded', { status: 429 });
    
    response.headers.set('X-RateLimit-Limit', limit.toString());
    response.headers.set('X-RateLimit-Remaining', remaining.toString());
    response.headers.set('X-RateLimit-Reset', new Date(reset).toISOString());
    response.headers.set('X-RateLimit-Type', rateLimitType);
    
    // Security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // CORS headers for API routes
    if (pathname.startsWith('/api/')) {
      response.headers.set('Access-Control-Allow-Origin', process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000');
      response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    }
    
    if (!success) {
      // Log rate limit violations for monitoring
      console.warn(`Rate limit exceeded for IP ${ip} on ${pathname} (type: ${rateLimitType})`);
      
      return new NextResponse(
        JSON.stringify({
          error: 'Rate limit exceeded',
          retryAfter: Math.round((reset - Date.now()) / 1000),
          limit,
          remaining: 0,
          type: rateLimitType
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'Retry-After': Math.round((reset - Date.now()) / 1000).toString(),
            ...Object.fromEntries(response.headers.entries())
          }
        }
      );
    }
    
    return response;
  } catch (error) {
    console.error('Rate limiting middleware error:', error);
    // Fail open - allow request if rate limiting fails
    return NextResponse.next();
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
