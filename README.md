# AI Knowledge Assistant with Supabase

A secure, production-ready AI chatbot built with Next.js and Supabase that responds using only approved knowledge base sources. Features **Google Gemini text-embedding-004** for superior semantic search capabilities and **Supabase** for scalable data persistence.

## 🚀 Features

### Core Functionality
- 🤖 **AI-Powered Responses**: Integration with OpenRouter API for intelligent responses
- 📚 **Knowledge Base Management**: Upload PDFs and add URLs as knowledge sources
- 🔍 **Enhanced Vector Search**: Choose between Google Gemini embeddings or simple embeddings
- 🧠 **Semantic Understanding**: Gemini text-embedding-004 provides better context understanding
- 🔐 **User Authentication**: Secure email/password authentication with <PERSON>pa<PERSON> Auth
- 📱 **Responsive Design**: Clean, modern UI that works on all devices

### Database & Storage
- 🗄️ **Supabase Integration**: Production-ready PostgreSQL database with real-time capabilities
- 🔒 **Row Level Security**: Secure data access with user-specific permissions
- 📊 **Vector Search**: pgvector extension for efficient similarity search
- 💾 **Persistent Storage**: All data stored securely in Supabase
- 🔄 **Real-time Updates**: Live synchronization across devices

### Advanced Features
- 🎯 **Multi-user Support**: Each user has their own isolated knowledge base
- 📈 **Analytics**: Track usage and knowledge base statistics
- 🔗 **Source URLs**: Clickable links to original documents
- 🛡️ **Rate Limiting**: Prevent abuse with configurable limits
- 🔧 **Debug Tools**: Advanced search debugging and analytics

## 🏗️ Architecture

### Technology Stack
- **Frontend**: Next.js 13, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Supabase
- **Database**: PostgreSQL with pgvector extension
- **Authentication**: Supabase Auth
- **AI/ML**: OpenRouter API, Google Gemini Embeddings
- **UI Components**: shadcn/ui, Radix UI

### Database Schema
```sql
-- Users (managed by Supabase Auth)
-- knowledge_sources: Store documents and URLs
-- document_embeddings: Vector embeddings for search
-- chat_sessions: User chat sessions
-- chat_messages: Individual messages with sources
```

## 🚀 Quick Start

### 1. Prerequisites
- Node.js 18+ and npm
- Supabase account
- OpenRouter API key (optional but recommended)
- Google Gemini API key (optional for enhanced embeddings)

### 2. Supabase Setup

1. **Create a new Supabase project** at [supabase.com](https://supabase.com)

2. **Enable required extensions** in SQL Editor:
```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable vector extension for embeddings
CREATE EXTENSION IF NOT EXISTS "vector";
```

3. **Run the database migrations**:
   - Copy the SQL from `supabase/migrations/20250614093624_violet_sun.sql`
   - Run it in your Supabase SQL Editor
   - Copy the SQL from `supabase/migrations/20250614093625_add_vector_search_function.sql`
   - Run it in your Supabase SQL Editor

4. **Configure Authentication**:
   - Go to Authentication > Settings
   - Disable "Enable email confirmations" for easier testing
   - Configure any additional auth providers if needed

### 3. Environment Setup

1. **Clone and install**:
```bash
git clone <your-repo>
cd ai-knowledge-assistant
npm install
```

2. **Configure environment variables**:
```bash
cp .env.example .env.local
```

3. **Add your API keys to `.env.local`**:
```env
# Required: Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Required: OpenRouter API Key
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Optional but Recommended: Google Gemini API Key
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Site URL
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

### 4. Get API Keys

#### Supabase Keys
1. Go to your Supabase project dashboard
2. Navigate to Settings > API
3. Copy the Project URL and anon public key
4. Copy the service_role secret key (keep this secure!)

#### OpenRouter API Key (Required for AI responses)
1. Visit [OpenRouter.ai](https://openrouter.ai/)
2. Sign up and create an API key
3. Add to your environment variables

#### Google Gemini API Key (Recommended for better search)
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add to your environment variables

### 5. Run the Application

```bash
npm run dev
```

Visit `http://localhost:3000` to access the application.

## 📖 Usage Guide

### Getting Started
1. **Sign Up**: Create an account at `/login`
2. **Add Knowledge**: Go to "Manage Knowledge" to upload PDFs or add URLs
3. **Start Chatting**: Use the dashboard to ask questions about your knowledge base

### Managing Knowledge Base
- **Upload PDFs**: Add documents with optional source URLs
- **Add Web URLs**: Automatically extract content from websites
- **Edit Source URLs**: Add clickable links to original sources
- **Debug Search**: Test and analyze search performance

### Chat Features
- **Semantic Search**: Ask questions in natural language
- **Source Attribution**: See which documents were used for answers
- **Clickable Sources**: Direct links to original documents
- **Rate Limiting**: 10 messages per minute to prevent abuse

## 🔧 Configuration

### Embedding Options

#### Google Gemini text-embedding-004 (Recommended)
- **Latest Gemini embedding model** with improved performance
- **Superior semantic understanding** and context awareness
- **Cost-effective** compared to OpenAI embeddings
- **Higher token limits** (10,000+ characters per request)

#### Simple Embeddings (Fallback)
- **No API key required**
- **Basic keyword matching**
- **Automatically used when Gemini API key is not provided**

### Supported AI Models (OpenRouter)
- `deepseek/deepseek-chat-v3-0324:free` (default, free)

## 🛡️ Security Features

### Authentication & Authorization
- **Supabase Auth**: Secure email/password authentication
- **Row Level Security**: Users can only access their own data
- **JWT Tokens**: Secure session management
- **Password Reset**: Built-in password recovery

### Data Protection
- **Encrypted Storage**: All data encrypted at rest in Supabase
- **Secure API**: All endpoints protected with authentication
- **Input Validation**: Comprehensive validation and sanitization
- **Rate Limiting**: Prevent abuse and ensure fair usage

## 📊 Database Schema

### Core Tables
```sql
knowledge_sources (
  id UUID PRIMARY KEY,
  title TEXT NOT NULL,
  type source_type NOT NULL, -- 'pdf' | 'url'
  source TEXT NOT NULL,
  source_url TEXT,
  content TEXT NOT NULL,
  topics TEXT[],
  status source_status DEFAULT 'processing',
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
)

document_embeddings (
  id UUID PRIMARY KEY,
  source_id UUID REFERENCES knowledge_sources(id),
  content_chunk TEXT NOT NULL,
  embedding VECTOR(768), -- Gemini embedding dimension
  chunk_index INTEGER NOT NULL
)

chat_sessions (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  title TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
)

chat_messages (
  id UUID PRIMARY KEY,
  session_id UUID REFERENCES chat_sessions(id),
  content TEXT NOT NULL,
  role message_role NOT NULL, -- 'user' | 'assistant'
  sources TEXT[],
  source_urls JSONB,
  from_knowledge_base BOOLEAN DEFAULT false
)
```

## 🚀 Deployment

### Vercel Deployment (Recommended)
1. **Connect to Vercel**:
   - Import your repository to Vercel
   - Configure environment variables in Vercel dashboard

2. **Environment Variables**:
   - Add all variables from `.env.local` to Vercel
   - Ensure `NEXT_PUBLIC_SITE_URL` points to your domain

3. **Deploy**:
   - Vercel will automatically deploy on push to main branch

### Other Platforms
- **Netlify**: Configure build settings and environment variables
- **Railway**: Connect repository and add environment variables
- **DigitalOcean App Platform**: Use the app spec configuration

## 🔍 API Endpoints

### Authentication
- `POST /api/auth` - Sign up, sign in, sign out, password reset

### Knowledge Management
- `GET /api/knowledge-sources` - Get user's knowledge sources
- `POST /api/knowledge-sources` - Create new knowledge source
- `PUT /api/knowledge-sources/[id]` - Update knowledge source
- `DELETE /api/knowledge-sources/[id]` - Delete knowledge source

### Content Processing
- `POST /api/upload-pdf-supabase` - Upload and process PDF
- `POST /api/process-url-supabase` - Process web URL

### Chat
- `POST /api/chat-supabase` - Send message and get AI response

## 🧪 Development

### Local Development
```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

### Database Development
```bash
# Reset database (if needed)
# Run migration files in Supabase SQL Editor

# Test vector search
# Use the debug tools in the admin panel
```

## 📈 Performance

### Optimization Features
- **Vector Indexing**: Efficient similarity search with pgvector
- **Content Chunking**: Large documents split for better processing
- **Caching**: Supabase built-in caching for faster queries
- **Rate Limiting**: Prevent overload and ensure stability

### Monitoring
- **Supabase Dashboard**: Monitor database performance
- **Vercel Analytics**: Track application performance
- **Error Tracking**: Built-in error handling and logging

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test with both embedding types
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

### Common Issues

1. **"Unauthorized" errors**: Check Supabase configuration and RLS policies
2. **Vector search not working**: Ensure pgvector extension is enabled
3. **Embedding generation fails**: Verify Gemini API key and rate limits
4. **File upload fails**: Check file size (max 10MB) and type (PDF only)

### Getting Help
- Check the [Supabase documentation](https://supabase.com/docs)
- Review the [OpenRouter API docs](https://openrouter.ai/docs)
- Open an issue in the repository

## 🔮 Roadmap

### Upcoming Features
- **File Storage**: Supabase Storage integration for file management
- **Real-time Chat**: Live chat updates with Supabase Realtime
- **Advanced Analytics**: Detailed usage and performance metrics
- **Team Collaboration**: Shared knowledge bases and permissions
- **API Access**: RESTful API for third-party integrations
- **Mobile App**: React Native mobile application

### Performance Improvements
- **Caching Layer**: Redis integration for faster responses
- **Background Processing**: Queue system for large file processing
- **CDN Integration**: Global content delivery for better performance