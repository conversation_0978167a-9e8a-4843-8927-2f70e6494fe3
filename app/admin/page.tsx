'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { 
  Upload, 
  Link as LinkIcon, 
  FileText, 
  Trash2, 
  Eye,
  Plus,
  CheckCircle,
  XCircle,
  Home,
  Database,
  Settings,
  AlertCircle,
  RefreshCw,
  Zap,
  Brain,
  Search,
  Bug,
  Edit,
  ExternalLink,
  Save,
  X,
  LogOut,
  Shield,
  UserPlus,
  Users
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { AuthService } from '@/lib/auth';
import { AdminAuthService } from '@/lib/admin-auth';
import { KnowledgeItem, isValidUrl, normalizeUrl } from '@/lib/knowledge-base-types';
import { SupabaseVectorStore } from '@/lib/supabase-vector-store';
import { KnowledgeSourcesDB } from '@/lib/database';

export default function AdminPage() {
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [knowledgeBase, setKnowledgeBase] = useState<KnowledgeItem[]>([]);
  const [newUrl, setNewUrl] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [pdfSourceUrl, setPdfSourceUrl] = useState('');
  const [previewContent, setPreviewContent] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [vectorStats, setVectorStats] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('sources');
  const [debugQuery, setDebugQuery] = useState('');
  const [debugResults, setDebugResults] = useState<any>(null);
  const [editingUrl, setEditingUrl] = useState<string | null>(null);
  const [editUrlValue, setEditUrlValue] = useState('');
  
  // Admin management state
  const [whitelistedAdmins, setWhitelistedAdmins] = useState<any[]>([]);
  const [newAdminEmail, setNewAdminEmail] = useState('');
  const [isInvitingAdmin, setIsInvitingAdmin] = useState(false);
  const [isActivatingAdmin, setIsActivatingAdmin] = useState<string | null>(null);
  const [isRemovingAdmin, setIsRemovingAdmin] = useState<string | null>(null);
  const [isLoadingAdmins, setIsLoadingAdmins] = useState(false);
  
  const router = useRouter();

  useEffect(() => {
    const initializeAuth = async () => {
      const currentUser = await AuthService.getCurrentUser();
      if (!currentUser) {
        router.push('/login');
        return;
      }
      
      // Check if user is whitelisted as admin
      const isWhitelisted = await AdminAuthService.isEmailWhitelisted(currentUser.email || '');
      if (!isWhitelisted) {
        toast.error('Access denied. You are not authorized to access the admin panel.');
        router.push('/dashboard');
        return;
      }
      
      setUser(currentUser);
      setIsAuthorized(true);
      await loadKnowledgeBase(currentUser.id);
      await loadWhitelistedAdmins();
      setIsLoading(false);
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = AuthService.onAuthStateChange(async (user) => {
      if (!user) {
        router.push('/login');
      } else {
        const isWhitelisted = await AdminAuthService.isEmailWhitelisted(user.email || '');
        if (!isWhitelisted) {
          router.push('/dashboard');
        } else {
          setUser(user);
          setIsAuthorized(true);
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [router]);

  const loadKnowledgeBase = async (userId: string) => {
    try {
      const sources = await KnowledgeSourcesDB.getAll(userId);
      setKnowledgeBase(sources);
      
      // Load vector store stats
      const vectorStore = new SupabaseVectorStore(userId);
      const stats = await vectorStore.getStats(userId);
      setVectorStats(stats);
    } catch (error) {
      console.error('Error loading knowledge base:', error);
      toast.error('Failed to load knowledge base');
    }
  };

  const loadWhitelistedAdmins = async () => {
    setIsLoadingAdmins(true);
    try {
      console.log('🔄 Loading whitelisted admins...');
      const admins = await AdminAuthService.getWhitelistedAdmins();
      console.log('📊 Loaded admins in component:', admins);
      console.log('📊 Setting admins state with length:', admins.length);
      
      // Ensure we're setting an array and force re-render
      const adminArray = Array.isArray(admins) ? admins : [];
      setWhitelistedAdmins(adminArray);
      
      // Force a small delay to ensure state is updated
      setTimeout(() => {
        console.log('📊 State should be updated now');
      }, 100);
      
    } catch (error) {
      console.error('Error loading whitelisted admins:', error);
      toast.error('Failed to load admin list');
      setWhitelistedAdmins([]); // Ensure we have an empty array on error
    } finally {
      setIsLoadingAdmins(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await AuthService.signOut();
      router.push('/login');
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Failed to sign out');
    }
  };

  const handleInviteAdmin = async () => {
    if (!newAdminEmail.trim() || !user) return;

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newAdminEmail)) {
      toast.error('Please enter a valid email address');
      return;
    }

    setIsInvitingAdmin(true);
    
    try {
      const result = await AdminAuthService.inviteAdmin(newAdminEmail, user.id);
      
      if (result.success) {
        toast.success(`Successfully invited ${newAdminEmail} as admin`);
        setNewAdminEmail('');
        await loadWhitelistedAdmins();
      } else {
        toast.error(result.error || 'Failed to invite admin');
      }
    } catch (error) {
      console.error('Error inviting admin:', error);
      toast.error('Failed to invite admin');
    } finally {
      setIsInvitingAdmin(false);
    }
  };

  const handleRemoveAdmin = async (email: string) => {
    setIsRemovingAdmin(email);
    
    try {
      console.log('🗑️ Attempting to remove admin:', email);
      const result = await AdminAuthService.removeAdmin(email);
      
      if (result.success) {
        toast.success(`Successfully removed ${email} from admin list`);
        await loadWhitelistedAdmins();
      } else {
        console.error('❌ Remove failed:', result.error);
        toast.error(result.error || 'Failed to remove admin');
      }
    } catch (error) {
      console.error('Error removing admin:', error);
      toast.error('Failed to remove admin');
    } finally {
      setIsRemovingAdmin(null);
    }
  };

  const handleActivateAdmin = async (email: string) => {
    setIsActivatingAdmin(email);
    
    try {
      console.log('⚡ Attempting to activate admin:', email);
      const result = await AdminAuthService.forceActivateAdmin(email);
      
      if (result.success) {
        toast.success(`Successfully activated ${email}`);
        await loadWhitelistedAdmins();
      } else {
        console.error('❌ Activation failed:', result.error);
        toast.error(result.error || 'Failed to activate admin');
      }
    } catch (error) {
      console.error('Error activating admin:', error);
      toast.error('Failed to activate admin');
    } finally {
      setIsActivatingAdmin(null);
    }
  };

  const handleFileUpload = async () => {
    if (!selectedFile || !user) return;

    // Validate source URL if provided
    if (pdfSourceUrl && !isValidUrl(pdfSourceUrl)) {
      toast.error('Please enter a valid URL (must start with http:// or https://)');
      return;
    }

    setIsProcessing(true);

    try {
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('No active session');
      }

      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('accessToken', session.access_token);
      if (pdfSourceUrl) {
        formData.append('sourceUrl', normalizeUrl(pdfSourceUrl));
      }
      const response = await fetch('/api/upload-pdf-supabase', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData?.error || 'Failed to upload file';
        const errorDetails = errorData?.details || '';
        
        console.error('Upload error details:', {
          status: response.status,
          statusText: response.statusText,
          error: errorData,
        });
        
        throw new Error(`${errorMessage}${errorDetails ? `\n\n${errorDetails}` : ''}`);
      }

      const result = await response.json();
      
      await loadKnowledgeBase(user.id);
      setSelectedFile(null);
      setPdfSourceUrl('');
      toast.success(`PDF uploaded and processed with ${result.embeddingType} embeddings`);
    } catch (error) {
      console.error('Error uploading file:', error);
      toast.error('Failed to upload PDF');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleUrlAdd = async () => {
    if (!newUrl.trim() || !user) return;

    if (!isValidUrl(newUrl)) {
      toast.error('Please enter a valid URL (must start with http:// or https://)');
      return;
    }

    setIsProcessing(true);
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('No active session');
      }
      
      const response = await fetch('/api/process-url-supabase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({ url: normalizeUrl(newUrl) }),
      });

      if (!response.ok) {
        throw new Error('Failed to process URL');
      }

      const result = await response.json();
      
      await loadKnowledgeBase(user.id);
      setNewUrl('');
      toast.success(`URL processed and added with ${result.embeddingType} embeddings`);
    } catch (error) {
      console.error('Error processing URL:', error);
      toast.error('Failed to process URL');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!user) return;

    try {
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('No active session');
      }

      const response = await fetch(`/api/knowledge-sources/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete source');
      }

      await loadKnowledgeBase(user.id);
      toast.success('Item removed from knowledge base');
    } catch (error) {
      console.error('Error deleting source:', error);
      toast.error('Failed to delete source');
    }
  };

  const handleEditUrl = (item: KnowledgeItem) => {
    setEditingUrl(item.id);
    setEditUrlValue(item.sourceUrl || '');
  };

  const handleSaveUrl = async (id: string) => {
    if (!user) return;

    // Validate URL if provided
    if (editUrlValue && !isValidUrl(editUrlValue)) {
      toast.error('Please enter a valid URL (must start with http:// or https://)');
      return;
    }

    try {
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('No active session');
      }

      const response = await fetch(`/api/knowledge-sources/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          sourceUrl: editUrlValue ? normalizeUrl(editUrlValue) : undefined
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update source URL');
      }

      await loadKnowledgeBase(user.id);
      setEditingUrl(null);
      setEditUrlValue('');
      toast.success('Source URL updated successfully');
    } catch (error) {
      console.error('Error updating source URL:', error);
      toast.error('Failed to update source URL');
    }
  };

  const handleCancelEdit = () => {
    setEditingUrl(null);
    setEditUrlValue('');
  };

  const handlePreview = (content: string) => {
    setPreviewContent(content);
  };

  const handleRefreshVectorStore = async () => {
    if (!user) return;

    try {
      await loadKnowledgeBase(user.id);
      toast.success('Knowledge base refreshed');
    } catch (error) {
      console.error('Error refreshing knowledge base:', error);
      toast.error('Failed to refresh knowledge base');
    }
  };

  const handleAddFirstSource = () => {
    setActiveTab('add');
  };

  const handleDebugSearch = async () => {
    if (!debugQuery.trim() || !user) return;
    
    try {
      const vectorStore = new SupabaseVectorStore(user.id);
      const searchResults = await vectorStore.searchWithKeywords(debugQuery, 10, 0.01, user.id);
      
      const debugInfo = {
        query: debugQuery,
        totalDocuments: knowledgeBase.length,
        searchResults: searchResults.length,
        embeddingType: vectorStats?.embeddingType || 'Unknown',
        documents: knowledgeBase.map(doc => ({
          id: doc.id,
          title: doc.title,
          type: doc.type,
          contentLength: doc.content.length,
          topics: doc.topics,
          contentPreview: doc.content.substring(0, 200) + '...',
          queryInTitle: doc.title.toLowerCase().includes(debugQuery.toLowerCase()),
          queryInContent: doc.content.toLowerCase().includes(debugQuery.toLowerCase()),
          queryInTopics: doc.topics.some(topic => 
            topic.toLowerCase().includes(debugQuery.toLowerCase()) ||
            debugQuery.toLowerCase().includes(topic.toLowerCase())
          )
        })),
        results: searchResults.map(result => ({
          title: result.document.metadata.title,
          score: result.score,
          matchType: result.matchType,
          topics: result.document.metadata.topics,
          contentPreview: result.document.content.substring(0, 300) + '...'
        }))
      };
      
      setDebugResults(debugInfo);
      console.log('=== DEBUG RESULTS ===', debugInfo);
      
    } catch (error) {
      console.error('Debug search error:', error);
      toast.error('Debug search failed');
    }
  };

  // Debug function to check admin status
  const handleDebugAdminStatus = async () => {
    console.log('🔍 Current whitelistedAdmins state:', whitelistedAdmins);
    console.log('🔍 whitelistedAdmins length:', whitelistedAdmins.length);
    console.log('🔍 whitelistedAdmins type:', typeof whitelistedAdmins);
    console.log('🔍 whitelistedAdmins isArray:', Array.isArray(whitelistedAdmins));
    
    await AdminAuthService.debugAdminStatus();
    await loadWhitelistedAdmins();
  };

  // Calculate active admins count safely with better logging
  const getActiveAdminsCount = () => {
    if (!Array.isArray(whitelistedAdmins)) {
      console.warn('⚠️ whitelistedAdmins is not an array:', whitelistedAdmins);
      return 0;
    }
    
    const activeAdmins = whitelistedAdmins.filter(admin => {
      const isValid = admin && typeof admin === 'object';
      const isActive = isValid && admin.is_active === true;
      
      if (isValid) {
        console.log(`👤 Admin ${admin.email}: is_active=${admin.is_active}, used_at=${admin.used_at}`);
      }
      
      return isActive;
    });
    
    const activeCount = activeAdmins.length;
    
    console.log('📊 Active admins calculation:', {
      totalAdmins: whitelistedAdmins.length,
      activeAdmins: activeCount,
      activeAdminEmails: activeAdmins.map(a => a.email),
      allAdmins: whitelistedAdmins.map(a => ({ 
        email: a?.email, 
        is_active: a?.is_active, 
        used_at: a?.used_at 
      }))
    });
    
    return activeCount;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!isAuthorized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 p-3 bg-red-500 rounded-full w-fit">
              <Shield className="w-6 h-6 text-white" />
            </div>
            <CardTitle>Access Denied</CardTitle>
            <p className="text-slate-600 text-sm">You are not authorized to access the admin panel</p>
          </CardHeader>
          <CardContent className="text-center">
            <Link href="/">
              <Button variant="outline">
                <Home className="w-4 h-4 mr-2" />
                Back to Home
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4 py-6 max-w-6xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-slate-800 rounded-lg">
              <Settings className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-slate-800">Admin Panel</h1>
              <p className="text-slate-600 text-sm">Manage knowledge base and admin access</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="flex items-center gap-1">
              <Shield className="w-3 h-3" />
              Admin: {user?.email}
            </Badge>
            <Button onClick={handleDebugAdminStatus} variant="outline" size="sm">
              <Bug className="w-4 h-4 mr-2" />
              Debug
            </Button>
            <Button onClick={handleRefreshVectorStore} variant="outline" size="sm">
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Link href="/">
              <Button variant="outline" size="sm">
                <Home className="w-4 h-4 mr-2" />
                Back to Chat
              </Button>
            </Link>
            <Button onClick={handleSignOut} variant="outline" size="sm">
              <LogOut className="w-4 h-4 mr-2" />
              Sign Out
            </Button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <Database className="w-5 h-5 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold">{knowledgeBase.length}</p>
                  <p className="text-sm text-slate-600">Total Sources</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <FileText className="w-5 h-5 text-green-500" />
                <div>
                  <p className="text-2xl font-bold">{knowledgeBase.filter(i => i.type === 'pdf').length}</p>
                  <p className="text-sm text-slate-600">PDF Documents</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <LinkIcon className="w-5 h-5 text-orange-500" />
                <div>
                  <p className="text-2xl font-bold">{knowledgeBase.filter(i => i.type === 'url').length}</p>
                  <p className="text-sm text-slate-600">Web Sources</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <Users className="w-5 h-5 text-purple-500" />
                <div>
                  <p className="text-2xl font-bold">
                    {isLoadingAdmins ? (
                      <div className="w-6 h-6 border-2 border-purple-500 border-t-transparent rounded-full animate-spin" />
                    ) : (
                      getActiveAdminsCount()
                    )}
                  </p>
                  <p className="text-sm text-slate-600">Active Admins</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Vector Store Status */}
        {vectorStats && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <Brain className="w-4 h-4" />
                Search Index Status
                <Badge variant={vectorStats.embeddingType === 'Gemini' ? 'default' : 'secondary'}>
                  {vectorStats.embeddingType} Embeddings
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-slate-600">Indexed Documents: <span className="font-medium">{vectorStats.totalDocuments}</span></p>
                  <p className="text-slate-600">Available Topics: <span className="font-medium">{vectorStats.totalTopics}</span></p>
                  <p className="text-slate-600">Embedding Type: <span className="font-medium">{vectorStats.embeddingType}</span></p>
                </div>
                <div>
                  <p className="text-slate-600">Document Types:</p>
                  <div className="flex gap-2 mt-1">
                    {Object.entries(vectorStats.documentTypes).map(([type, count]) => (
                      <Badge key={type} variant="outline" className="text-xs">
                        {type}: {count as number}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
              {vectorStats.embeddingType === 'Simple' && (
                <Alert className="mt-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Using simple embeddings. Add GEMINI_API_KEY to environment variables for enhanced semantic search.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="sources">Knowledge Sources</TabsTrigger>
            <TabsTrigger value="add">Add Content</TabsTrigger>
            <TabsTrigger value="admins">Admin Management</TabsTrigger>
            <TabsTrigger value="debug">Debug Search</TabsTrigger>
          </TabsList>

          <TabsContent value="sources" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="w-5 h-5" />
                  Your Knowledge Base Sources
                </CardTitle>
              </CardHeader>
              <CardContent>
                {knowledgeBase.length === 0 ? (
                  <div className="text-center py-8">
                    <AlertCircle className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-slate-600 mb-2">No sources added yet</h3>
                    <p className="text-slate-500 mb-4">Add PDFs or web URLs to build your personal knowledge base</p>
                    <Button onClick={handleAddFirstSource}>
                      <Plus className="w-4 h-4 mr-2" />
                      Add First Source
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {knowledgeBase.map((item) => (
                      <div key={item.id} className="border border-slate-200 rounded-lg p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              {item.type === 'pdf' ? (
                                <FileText className="w-4 h-4 text-red-500" />
                              ) : (
                                <LinkIcon className="w-4 h-4 text-blue-500" />
                              )}
                              <h4 className="font-medium">{item.title}</h4>
                              <Badge variant={item.status === 'active' ? 'default' : 'secondary'}>
                                {item.status}
                              </Badge>
                              {item.sourceUrl && (
                                <Badge variant="outline" className="text-xs">
                                  <ExternalLink className="w-3 h-3 mr-1" />
                                  Has URL
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-slate-600 mb-2">{item.source}</p>
                            
                            {/* Source URL Management */}
                            <div className="mb-2">
                              {editingUrl === item.id ? (
                                <div className="flex items-center gap-2">
                                  <Input
                                    value={editUrlValue}
                                    onChange={(e) => setEditUrlValue(e.target.value)}
                                    placeholder="https://example.com/document.pdf"
                                    className="text-sm"
                                  />
                                  <Button
                                    size="sm"
                                    onClick={() => handleSaveUrl(item.id)}
                                    className="px-2"
                                  >
                                    <Save className="w-3 h-3" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={handleCancelEdit}
                                    className="px-2"
                                  >
                                    <X className="w-3 h-3" />
                                  </Button>
                                </div>
                              ) : (
                                <div className="flex items-center gap-2">
                                  {item.sourceUrl ? (
                                    <a
                                      href={item.sourceUrl}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="text-sm text-blue-600 hover:text-blue-800 underline flex items-center gap-1"
                                    >
                                      <ExternalLink className="w-3 h-3" />
                                      {item.sourceUrl}
                                    </a>
                                  ) : (
                                    <span className="text-sm text-slate-500">No source URL</span>
                                  )}
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => handleEditUrl(item)}
                                    className="px-2 h-6"
                                  >
                                    <Edit className="w-3 h-3" />
                                  </Button>
                                </div>
                              )}
                            </div>

                            <p className="text-xs text-slate-500">
                              Added {item.addedAt.toLocaleDateString()} at {item.addedAt.toLocaleTimeString()}
                            </p>
                            <p className="text-xs text-slate-500 mt-1">
                              Content length: {item.content.length} characters
                              {item.fileSize && ` | File size: ${(item.fileSize / 1024 / 1024).toFixed(2)} MB`}
                            </p>
                            {item.topics.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-2">
                                {item.topics.slice(0, 10).map((topic, index) => (
                                  <Badge key={index} variant="outline" className="text-xs">
                                    {topic}
                                  </Badge>
                                ))}
                                {item.topics.length > 10 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{item.topics.length - 10} more
                                  </Badge>
                                )}
                              </div>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handlePreview(item.content)}
                                >
                                  <Eye className="w-4 h-4" />
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="max-w-3xl max-h-[80vh]">
                                <DialogHeader>
                                  <DialogTitle>{item.title}</DialogTitle>
                                  <DialogDescription>Content preview</DialogDescription>
                                </DialogHeader>
                                <div className="mt-4 max-h-96 overflow-y-auto">
                                  <pre className="whitespace-pre-wrap text-sm bg-slate-50 p-4 rounded">
                                    {item.content.substring(0, 2000)}
                                    {item.content.length > 2000 && '...'}
                                  </pre>
                                </div>
                              </DialogContent>
                            </Dialog>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Source</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to remove "{item.title}" from the knowledge base? This action cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDelete(item.id)}
                                    className="bg-red-600 hover:bg-red-700"
                                  >
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="add" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* PDF Upload */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Upload className="w-5 h-5" />
                    Upload PDF Document
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="pdf-upload">Choose PDF File</Label>
                    <Input
                      id="pdf-upload"
                      type="file"
                      accept=".pdf"
                      onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                      disabled={isProcessing}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="pdf-source-url">Source URL (Optional)</Label>
                    <Input
                      id="pdf-source-url"
                      type="url"
                      value={pdfSourceUrl}
                      onChange={(e) => setPdfSourceUrl(e.target.value)}
                      placeholder="https://example.com/document.pdf"
                      disabled={isProcessing}
                    />
                    <p className="text-xs text-slate-500 mt-1">
                      Provide a URL where this PDF can be accessed online
                    </p>
                  </div>

                  {selectedFile && (
                    <Alert>
                      <CheckCircle className="h-4 w-4" />
                      <AlertDescription>
                        Selected: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                      </AlertDescription>
                    </Alert>
                  )}
                  
                  <Button 
                    onClick={handleFileUpload} 
                    disabled={!selectedFile || isProcessing}
                    className="w-full"
                  >
                    {isProcessing ? 'Processing...' : 'Upload & Process PDF'}
                  </Button>
                </CardContent>
              </Card>

              {/* URL Input */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <LinkIcon className="w-5 h-5" />
                    Add Website URL
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="url-input">Website URL</Label>
                    <Input
                      id="url-input"
                      type="url"
                      value={newUrl}
                      onChange={(e) => setNewUrl(e.target.value)}
                      placeholder="https://example.com/article"
                      disabled={isProcessing}
                    />
                  </div>
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Only publicly accessible URLs are supported. The system will extract text content automatically and use the URL as the source link.
                    </AlertDescription>
                  </Alert>
                  <Button 
                    onClick={handleUrlAdd} 
                    disabled={!newUrl.trim() || isProcessing}
                    className="w-full"
                  >
                    {isProcessing ? 'Processing...' : 'Add URL'}
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="admins" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Invite New Admin */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <UserPlus className="w-5 h-5" />
                    Invite New Admin
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="admin-email">Admin Email Address</Label>
                    <Input
                      id="admin-email"
                      type="email"
                      value={newAdminEmail}
                      onChange={(e) => setNewAdminEmail(e.target.value)}
                      placeholder="<EMAIL>"
                      disabled={isInvitingAdmin}
                    />
                    <p className="text-xs text-slate-500 mt-1">
                      The invited user will be able to access the admin panel after signing up
                    </p>
                  </div>
                  
                  <Button 
                    onClick={handleInviteAdmin} 
                    disabled={!newAdminEmail.trim() || isInvitingAdmin}
                    className="w-full"
                  >
                    {isInvitingAdmin ? 'Inviting...' : 'Invite Admin'}
                  </Button>
                </CardContent>
              </Card>

              {/* Current Admins */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    Current Admins ({isLoadingAdmins ? '...' : getActiveAdminsCount()})
                    {isLoadingAdmins && (
                      <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin ml-2" />
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {isLoadingAdmins ? (
                      <div className="text-center py-4">
                        <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2" />
                        <p className="text-sm text-slate-500">Loading admins...</p>
                      </div>
                    ) : Array.isArray(whitelistedAdmins) && whitelistedAdmins.filter(admin => admin && admin.is_active).length > 0 ? (
                      whitelistedAdmins.filter(admin => admin && admin.is_active).map((admin) => (
                        <div key={admin.id} className="flex items-center justify-between p-3 border border-slate-200 rounded-lg">
                          <div className="flex-1">
                            <p className="font-medium text-sm">{admin.email}</p>
                            <p className="text-xs text-slate-500">
                              Invited {new Date(admin.invited_at).toLocaleDateString()}
                              {admin.used_at && ` • Signed up ${new Date(admin.used_at).toLocaleDateString()}`}
                            </p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={admin.used_at ? 'default' : 'secondary'} className="text-xs">
                              {admin.used_at ? 'Active' : 'Pending'}
                            </Badge>
                            {!admin.used_at && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleActivateAdmin(admin.email)}
                                disabled={isActivatingAdmin === admin.email}
                                className="text-green-600 hover:text-green-700"
                                title="Force activate admin"
                              >
                                {isActivatingAdmin === admin.email ? (
                                  <div className="w-3 h-3 border-2 border-green-600 border-t-transparent rounded-full animate-spin" />
                                ) : (
                                  <Zap className="w-3 h-3" />
                                )}
                              </Button>
                            )}
                            {admin.email !== user?.email && (
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button 
                                    variant="outline" 
                                    size="sm" 
                                    className="text-red-600 hover:text-red-700"
                                    disabled={isRemovingAdmin === admin.email}
                                  >
                                    {isRemovingAdmin === admin.email ? (
                                      <div className="w-3 h-3 border-2 border-red-600 border-t-transparent rounded-full animate-spin" />
                                    ) : (
                                      <Trash2 className="w-3 h-3" />
                                    )}
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Remove Admin</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      Are you sure you want to remove "{admin.email}" from the admin list? They will lose access to the admin panel.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => handleRemoveAdmin(admin.email)}
                                      className="bg-red-600 hover:bg-red-700"
                                    >
                                      Remove
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            )}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4">
                        <p className="text-slate-500 text-sm">No active admins found</p>
                        <p className="text-xs text-slate-400 mt-1">
                          Total admins in system: {Array.isArray(whitelistedAdmins) ? whitelistedAdmins.length : 0}
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="debug" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bug className="w-5 h-5" />
                  Debug Search System
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="debug-query">Test Search Query</Label>
                  <div className="flex gap-2">
                    <Input
                      id="debug-query"
                      value={debugQuery}
                      onChange={(e) => setDebugQuery(e.target.value)}
                      placeholder="Enter search query (e.g., 'Cara daftar LPDP')"
                    />
                    <Button onClick={handleDebugSearch} disabled={!debugQuery.trim()}>
                      <Search className="w-4 h-4 mr-2" />
                      Debug Search
                    </Button>
                  </div>
                </div>

                {debugResults && (
                  <div className="space-y-4">
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Query: "{debugResults.query}" | Documents: {debugResults.totalDocuments} | Results: {debugResults.searchResults} | Embedding: {debugResults.embeddingType}
                      </AlertDescription>
                    </Alert>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      {/* Documents Analysis */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm">Documents Analysis</CardTitle>
                        </CardHeader>
                        <CardContent className="max-h-96 overflow-y-auto">
                          {debugResults.documents.map((doc: any, index: number) => (
                            <div key={index} className="border-b border-slate-200 pb-3 mb-3 last:border-b-0">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium text-sm">{doc.title}</h4>
                                <Badge variant="outline" className="text-xs">{doc.type}</Badge>
                              </div>
                              <p className="text-xs text-slate-600 mb-2">
                                Length: {doc.contentLength} chars | Topics: {doc.topics.length}
                              </p>
                              <div className="flex gap-1 mb-2">
                                {doc.queryInTitle && <Badge variant="default" className="text-xs">Title Match</Badge>}
                                {doc.queryInContent && <Badge variant="default" className="text-xs">Content Match</Badge>}
                                {doc.queryInTopics && <Badge variant="default" className="text-xs">Topic Match</Badge>}
                                {!doc.queryInTitle && !doc.queryInContent && !doc.queryInTopics && (
                                  <Badge variant="secondary" className="text-xs">No Direct Match</Badge>
                                )}
                              </div>
                              <p className="text-xs text-slate-500">{doc.contentPreview}</p>
                              {doc.topics.length > 0 && (
                                <div className="flex flex-wrap gap-1 mt-2">
                                  {doc.topics.slice(0, 5).map((topic: string, i: number) => (
                                    <Badge key={i} variant="outline" className="text-xs">
                                      {topic}
                                    </Badge>
                                  ))}
                                </div>
                              )}
                            </div>
                          ))}
                        </CardContent>
                      </Card>

                      {/* Search Results */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm">Search Results</CardTitle>
                        </CardHeader>
                        <CardContent className="max-h-96 overflow-y-auto">
                          {debugResults.results.length === 0 ? (
                            <p className="text-sm text-slate-500">No search results found</p>
                          ) : (
                            debugResults.results.map((result: any, index: number) => (
                              <div key={index} className="border-b border-slate-200 pb-3 mb-3 last:border-b-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <h4 className="font-medium text-sm">{result.title}</h4>
                                  <Badge variant="default" className="text-xs">{result.matchType}</Badge>
                                </div>
                                <p className="text-xs text-slate-600 mb-2">
                                  Score: {(result.score * 100).toFixed(1)}%
                                </p>
                                <p className="text-xs text-slate-500 mb-2">{result.contentPreview}</p>
                                {result.topics.length > 0 && (
                                  <div className="flex flex-wrap gap-1">
                                    {result.topics.slice(0, 5).map((topic: string, i: number) => (
                                      <Badge key={i} variant="outline" className="text-xs">
                                        {topic}
                                      </Badge>
                                    ))}
                                  </div>
                                )}
                              </div>
                            ))
                          )}
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}