import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const code = searchParams.get('code')
  const access_token = searchParams.get('access_token')
  const refresh_token = searchParams.get('refresh_token')
  const type = searchParams.get('type')

  if (code) {
    // Handle code-based auth (email confirmation)
    const { error } = await supabase.auth.exchangeCodeForSession(code)
    
    if (error) {
      console.error('Error exchanging code for session:', error)
      return NextResponse.redirect(new URL('/login?error=confirmation_failed', request.url))
    }
  } else if (access_token && refresh_token) {
    // Handle token-based auth (direct token from email)
    const { error } = await supabase.auth.setSession({
      access_token,
      refresh_token
    })
    
    if (error) {
      console.error('Error setting session:', error)
      return NextResponse.redirect(new URL('/login?error=confirmation_failed', request.url))
    }
  }

  // Redirect to dashboard on successful confirmation
  if (type === 'signup') {
    return NextResponse.redirect(new URL('/dashboard?confirmed=true', request.url))
  }

  return NextResponse.redirect(new URL('/dashboard', request.url))
}