import { NextRequest, NextResponse } from 'next/server';
import { OpenRouterClient } from '@/lib/openrouter';
import { SupabaseVectorStore } from '@/lib/supabase-vector-store';
import { SecurityUtils, SECURITY_CONFIG } from '@/lib/security-config';

export const dynamic = 'force-dynamic';

// Get client IP for logging
function getClientIP(request: NextRequest): string {
  return request.headers.get('x-forwarded-for')?.split(',')[0] ||
         request.headers.get('x-real-ip') ||
         request.ip ||
         'unknown';
}

export async function POST(request: NextRequest) {
  const clientIP = getClientIP(request);

  try {
    const { message } = await request.json();

    // Input validation and sanitization
    if (!message || typeof message !== 'string') {
      SecurityUtils.logSecurityEvent({
        type: 'suspicious_activity',
        ip: clientIP,
        endpoint: '/api/chat-public',
        details: { reason: 'Invalid message format', message: typeof message }
      });

      return NextResponse.json(
        { error: 'Invalid message format' },
        { status: 400 }
      );
    }

    // Sanitize input
    const sanitizedMessage = SecurityUtils.sanitizeInput(message);

    if (sanitizedMessage.length === 0) {
      return NextResponse.json(
        { error: 'Message cannot be empty' },
        { status: 400 }
      );
    }

    if (sanitizedMessage.length > SECURITY_CONFIG.CONTENT.MAX_QUERY_LENGTH) {
      return NextResponse.json(
        { error: `Message too long. Maximum ${SECURITY_CONFIG.CONTENT.MAX_QUERY_LENGTH} characters allowed.` },
        { status: 400 }
      );
    }

    // Handle special stats request
    if (message === '__GET_STATS__') {
      const vectorStore = new SupabaseVectorStore();
      const stats = await vectorStore.getStats();
      return NextResponse.json({
        availableTopics: stats.topics,
        availableSources: stats.sources
      });
    }

    console.log(`🔍 Processing public query: "${message}"`);

    // Initialize Supabase vector store with admin access (no user filtering)
    const vectorStore = new SupabaseVectorStore();

    // Get all documents from the knowledge base (public access)
    const allDocuments = await vectorStore.getAllDocuments();
    
    if (allDocuments.length === 0) {
      return NextResponse.json({
        response: "I apologize, but no knowledge base sources have been added yet. The knowledge base is currently empty.",
        fromKnowledgeBase: false
      });
    }

    // Search for relevant documents using enhanced vector similarity
    const searchResults = await vectorStore.searchWithKeywords(message, 5, 0.02);
    
    if (searchResults.length === 0) {
      // Get available information for user guidance
      const stats = await vectorStore.getStats();
      
      console.log(`❌ No search results found for: "${message}"`);
      console.log(`📋 Available topics: ${stats.topics.slice(0, 10).join(', ')}`);
      console.log(`📄 Available sources: ${stats.sources.join(', ')}`);
      
      return NextResponse.json({
        response: `Aku tidak bisa menemukan informasi yang kamu cari. 

Ini topik yang bisa aku bantu: ${stats.topics.slice(0, 5).join(', ')}${stats.topics.length > 5 ? ', dll.' : ''}.

Sumber yang aku punya: ${stats.sources.slice(0, 3).join(', ')}${stats.sources.length > 3 ? ', and others' : ''}.

Coba ubah pertanyaanmu agar sesuai dengan topik-topik tersebut.`,
        fromKnowledgeBase: false,
        availableTopics: stats.topics.slice(0, 10),
        availableSources: stats.sources.slice(0, 5)
      });
    }

    // Prepare context from search results
    const context = searchResults.map(result => 
      `Source: ${result.document.metadata.title}\nContent: ${result.document.content}`
    ).join('\n\n');

    // Collect sources and source URLs
    const sources = [...new Set(searchResults.map(result => result.document.metadata.title))];
    const sourceUrls: { [title: string]: string } = {};
    
    searchResults.forEach(result => {
      const title = result.document.metadata.title;
      const url = result.document.metadata.sourceUrl;
      if (url) {
        sourceUrls[title] = url;
      }
    });

    console.log(`✅ Found ${searchResults.length} relevant documents`);
    console.log(`📄 Sources: ${sources.join(', ')}`);

    // Check if OpenRouter API key is available
    const openRouterApiKey = process.env.OPENROUTER_API_KEY;
    
    let response: string;
    const embeddingType = await vectorStore.getStats().then(stats => stats.embeddingType);
    
    if (!openRouterApiKey) {
      // Fallback to simple response generation
      const bestMatch = searchResults[0];
      response = `Based on the information in the knowledge base:

${bestMatch.document.content.substring(0, 600)}${bestMatch.document.content.length > 600 ? '...' : ''}

Source: ${bestMatch.document.metadata.title}
Match Type: ${bestMatch.matchType}
Relevance Score: ${(bestMatch.score * 100).toFixed(1)}%

Note: AI enhancement temporarily unavailable, showing direct knowledge base content.`;
    } else {
      // Use OpenRouter for intelligent response generation
      try {
        const openRouter = new OpenRouterClient(openRouterApiKey);
        response = await openRouter.generateResponse(message, context);
      } catch (aiError) {
        console.error('OpenRouter API error:', aiError);
        
        // Fallback response with better formatting
        const bestMatch = searchResults[0];
        response = `Based on the information in the knowledge base:

${bestMatch.document.content.substring(0, 600)}${bestMatch.document.content.length > 600 ? '...' : ''}

Source: ${bestMatch.document.metadata.title}
Match Type: ${bestMatch.matchType}
Relevance Score: ${(bestMatch.score * 100).toFixed(1)}%

Note: AI enhancement temporarily unavailable, showing direct knowledge base content.`;
      }
    }

    return NextResponse.json({
      response,
      fromKnowledgeBase: true,
      source: sources.join(', '),
      sourceUrls: Object.keys(sourceUrls).length > 0 ? sourceUrls : undefined,
      searchResults: searchResults.length,
      searchScores: searchResults.map(r => (r.score * 100).toFixed(1)),
      embeddingType
    });
    
  } catch (error) {
    console.error('Public chat API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
